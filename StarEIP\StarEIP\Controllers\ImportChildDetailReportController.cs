using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models.Auth;
using StarEIP.Models.Import;

namespace StarEIP.Controllers
{
    [Route("api/import-child-detail-report")]
    [ApiController]
    [Authorize(Policy = nameof(UserPermission.AllowDataImport))]
    public class ImportChildDetailReportController : ControllerBase
    {
        private readonly StarEipDbContext _dbContext;
        private readonly ILogger<ImportChildDetailReportController> _logger;

        public ImportChildDetailReportController(StarEipDbContext dbContext, ILogger<ImportChildDetailReportController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                _dbContext.ChangeTracker.LazyLoadingEnabled = true;
                _dbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                var query = _dbContext.Set<ImportChildDetailReport>().AsQueryable();

                loadOptions.PrimaryKey = new[] { nameof(ImportChildDetailReport.Id) };
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync(query, loadOptions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching import child detail reports");
                return StatusCode(500, "An error occurred while fetching the data");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var report = await _dbContext.Set<ImportChildDetailReport>().FindAsync(id);

                if (report == null)
                {
                    return NotFound();
                }

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching import child detail report by id");
                return StatusCode(500, "An error occurred while fetching the data");
            }
        }

        [HttpGet("by-ei-child-id/{eiChildId}")]
        public async Task<IActionResult> GetByEiChildId(string eiChildId)
        {
            try
            {
                if (string.IsNullOrEmpty(eiChildId))
                {
                    return BadRequest("EI Child ID cannot be empty");
                }

                var report = await _dbContext.Set<ImportChildDetailReport>()
                    .FirstOrDefaultAsync(r => r.EiChildId == eiChildId);

                if (report == null)
                {
                    return NotFound($"No child detail report found for EI Child ID: {eiChildId}");
                }

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching import child detail report by EI Child ID: {EiChildId}", eiChildId);
                return StatusCode(500, "An error occurred while fetching the data");
            }
        }
    }
}
