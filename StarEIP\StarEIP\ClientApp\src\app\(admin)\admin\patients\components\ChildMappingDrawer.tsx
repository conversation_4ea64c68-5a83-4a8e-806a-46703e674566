"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Group,
  Text,
  ActionIcon,
  Tabs,
  Loader,
  Badge,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import PatientTable from "@/app/(admin)/admin/patients/PatientTable";
import PatientForm from "@/app/(admin)/admin/patients/PatientForm";
import axios from "axios";
import { showNotification } from "@/app/Utils/notificationUtils";
import urlHelpers from "@/app/urlHelpers";
import { ChildDetailsDtoSchema } from "@/api/types";
import ImportChildInfoAuthorizationTable from "./ImportChildInfoAuthorizationTable";

interface ChildMappingDrawerProps {
  eiNumber: string;
  onMappingSuccess: () => void;
  renderDetails?: () => React.ReactNode;
  buttonText?: React.ReactNode;
  buttonSize?: "xs" | "sm" | "md" | "lg" | "xl";
  childData?: ChildDetailsDtoSchema;
}

const ChildMappingDrawer: React.FC<ChildMappingDrawerProps> = ({
  eiNumber,
  onMappingSuccess,
  buttonText = "Map to Child",
  buttonSize = "xs",
  childData,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [activeTab, setActiveTab] = useState<string | null>("search");
  const [physicians, setPhysicians] = useState<any[]>([]);
  const [lookupChildData, setLookupChildData] = useState<ChildDetailsDtoSchema | undefined>(childData);
  const [isLoadingLookup, setIsLoadingLookup] = useState(false);

  useEffect(() => {
    // Fetch physicians and optionally lookup child data when the drawer is opened
    if (opened) {
      const fetchPhysicians = async () => {
        try {
          const response = await axios.get(
            urlHelpers.getAbsoluteURL("api/physicians"),
          );
          setPhysicians(response.data.data);
        } catch (error) {
          console.error("Error fetching physicians:", error);
        }
      };

      const fetchChildDetailReport = async () => {
        if (eiNumber) {
          setIsLoadingLookup(true);
          try {
            const response = await axios.get(
              urlHelpers.getAbsoluteURL(`api/import-child-detail-report/by-ei-child-id/${eiNumber}`),
            );
            console.log('getting data', response.data);
            const reportData = response.data;

            // Transform the report data to match the childData interface
            const transformedData: ChildDetailsDtoSchema = {
              firstName: reportData.childFirstName,
              lastName: reportData.childLastName,
              dateOfBirth: reportData.dob,
              fullAddress: reportData.address + (reportData.city ? ", " + reportData.city : "") + (reportData.state ? ", " + reportData.state : "") + (reportData.zip ? " " + reportData.zip : ""),
              gender: reportData.gender,
              primaryLanguage: reportData.primaryLanguage,
              reasonForReferral: reportData.referralReason,
              referralMethod: "HUB",
              parentName: reportData.guardianFirstName + " " + reportData.guardianLastName,
              parentPhoneNumber: reportData.guardianPhone,
              programId: parseInt(eiNumber, 10) || undefined,
              status: "New",
            };
            console.log('transformed data', transformedData);
            setLookupChildData(transformedData);
          } catch (error) {
            console.error("Error fetching child detail report:", error);
            // Don't show error notification as this is optional data
            setLookupChildData(undefined);
          } finally {
            setIsLoadingLookup(false);
          }
        }
      };

      fetchPhysicians();
      fetchChildDetailReport();
    }
  }, [opened, eiNumber]);

  const handleLinkChild = async (child: ChildDetailsDtoSchema | null) => {
    if (!child) return;

    // Validate inputs before making the API call
    if (!eiNumber || eiNumber.trim() === "") {
      showNotification("error", "EI Number cannot be empty");
      return;
    }

    if (!child.id || child.id <= 0) {
      showNotification("error", "Invalid child selected");
      return;
    }

    try {
      console.log("Mapping EI Number:", eiNumber, "to Child ID:", child.id);
      const response = await axios.post(
        urlHelpers.getAbsoluteURL("api/children/map-ei-number"),
        {
          eiNumber: eiNumber.trim(),
          childId: child.id,
        },
      );

      showNotification(
        "success",
        response.data.message || "Successfully mapped EI Number to child",
      );

      onMappingSuccess();
      close();
    } catch (error: any) {
      console.error("Error mapping child:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.title ||
        (typeof error.response?.data === "string"
          ? error.response.data
          : null) ||
        error.message ||
        "Failed to map child";
      showNotification("error", errorMessage);
    }
  };

  const handleCreateChild = async (child: ChildDetailsDtoSchema) => {
    try {
      // First create the child
      const createUrl = urlHelpers.getAbsoluteURL("api/children/create");
      const createResponse = await axios.post(createUrl, child);

      if (createResponse.status === 200) {
        // Then map the EI number to the newly created child
        const newChildId = createResponse.data.id;
        await axios.post(
          urlHelpers.getAbsoluteURL("api/children/map-ei-number"),
          {
            eiNumber: eiNumber,
            childId: newChildId,
          },
        );

        showNotification(
          "success",
          "Successfully created new child and mapped EI Number",
        );

        onMappingSuccess();
        close();
      }
    } catch (error: any) {
      console.error("Error creating child:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.title ||
        (typeof error.response?.data === "string"
          ? error.response.data
          : null) ||
        error.message ||
        "Failed to create child";
      showNotification("error", errorMessage);
    }
  };


  // let mappedGender: string | undefined = undefined;
  // if (effectiveChildData?.gender) {
  //   const genderLower = effectiveChildData.gender.toLowerCase();
  //   if (genderLower === "m" || genderLower === "male") {
  //     mappedGender = "M";
  //   } else if (genderLower === "f" || genderLower === "female") {
  //     mappedGender = "F";
  //   } else {
  //     mappedGender = effectiveChildData.gender;
  //   }
  // }

  // Check if buttonText is a React element (icon)
  const isIcon = React.isValidElement(buttonText);

  return (
    <>
      {isIcon ? (
        <ActionIcon
          onClick={open}
          size={buttonSize}
          color="gray"
          variant="subtle"
          radius="xl"
        >
          {buttonText}
        </ActionIcon>
      ) : (
        <Button onClick={open} size={buttonSize}>
          {buttonText}
        </Button>
      )}

      <Drawer
        opened={opened}
        onClose={close}
        position="right"
        size="50%"
        padding="xs"
        styles={{
          content: { display: "flex", flexDirection: "column" },
          body: {
            display: "flex",
            flexDirection: "column",
            flex: 1,
            overflow: "auto",
          },
        }}
        overlayProps={{ backgroundOpacity: 0.1, blur: 0 }}
        closeOnClickOutside
        title={
          <Group gap="xs" align="center">
            <Text>Map EI Number: {eiNumber}</Text>
            {isLoadingLookup && <Loader size="xs" />}
            {lookupChildData && (
              <Badge size="xs" color="green" variant="light">
                Data loaded from Import Report
              </Badge>
            )}
          </Group>
        }
      >
        <Tabs
          value={activeTab}
          onChange={setActiveTab}
          styles={{
            root: {
              display: "flex",
              flexDirection: "column",
              flex: 1,
              overflow: "auto",
            },
          }}
        >
          <Tabs.List>
            <Tabs.Tab value="search">Search Existing Patient</Tabs.Tab>
            <Tabs.Tab value="create">Add New Patient</Tabs.Tab>
            <Tabs.Tab value="hub_auth">HUB Authorizations</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel
            value="search"
            pt="xs"
            style={{
              height: "100%",
              display: "flex",
              flex: 1,
              flexGrow: 1,
              flexDirection: "column",
              width: "100%",
              overflow: "auto",
            }}
          >
            <PatientTable
              showToolbar
              item={null}
              onFocusedChildChanging={handleLinkChild}
              options={{
                showDateOfBirth: true,
                showHeaderFilter: true,
                defaultFilter: lookupChildData?.dateOfBirth ? ["dateOfBirth", "=", new Date(lookupChildData.dateOfBirth)] : [],
              }}
            />
          </Tabs.Panel>

          <Tabs.Panel
            value="create"
            pt="xs"
            style={{
              height: "100%",
              display: "flex",
              flex: 1,
              flexGrow: 1,
              flexDirection: "column",
              width: "100%",
            }}
          >
            <PatientForm
              onSubmit={handleCreateChild}
              onCancel={() => setActiveTab("search")}
              isEditMode={false}
              initialData={lookupChildData}
              physicians={physicians}
            />
          </Tabs.Panel>

          <Tabs.Panel
              value="hub_auth"
              style={{ height: "100%", width: "100%", display: "flex", flex: 1, flexGrow: 1 }}
            >
              <ImportChildInfoAuthorizationTable
                programId={eiNumber?.toString()}
              />
            </Tabs.Panel>
        </Tabs>
      </Drawer>
    </>
  );
};

export default ChildMappingDrawer;
